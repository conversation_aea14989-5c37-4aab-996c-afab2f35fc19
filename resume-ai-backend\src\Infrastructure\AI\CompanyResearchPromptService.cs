using Microsoft.Extensions.Logging;
using System.Reflection;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace Infrastructure.AI;

public interface ICompanyResearchPromptService
{
    string GetSystemMessage();
    string GetUserPrompt(string companyUrl, string jobTitle);
    string GetInstructions(string? companyType = null);
    string GetResponseFormat();
    CompanyResearchPromptConfiguration GetConfiguration();
}

internal sealed class CompanyResearchPromptService : ICompanyResearchPromptService
{
    private readonly ILogger<CompanyResearchPromptService> _logger;
    private readonly Lazy<CompanyResearchPromptConfiguration> _configuration;

    public CompanyResearchPromptService(ILogger<CompanyResearchPromptService> logger)
    {
        _logger = logger;
        _configuration = new Lazy<CompanyResearchPromptConfiguration>(LoadConfiguration);
    }

    public string GetSystemMessage()
    {
        return _configuration.Value.Prompts.CompanyResearch.SystemMessage;
    }

    public string GetUserPrompt(string companyUrl, string jobTitle)
    {
        var template = _configuration.Value.Prompts.CompanyResearch.UserPromptTemplate;
        var instructions = GetInstructions();
        var responseFormat = GetResponseFormat();

        return template
            .Replace("{company_url}", companyUrl)
            .Replace("{job_title}", jobTitle)
            .Replace("{instructions}", instructions)
            .Replace("{response_format}", responseFormat);
    }

    public string GetInstructions(string? companyType = null)
    {
        var baseInstructions = _configuration.Value.Prompts.CompanyResearch.Instructions;

        if (!string.IsNullOrEmpty(companyType) && 
            _configuration.Value.CompanyTypes.TryGetValue(companyType, out var typeConfig))
        {
            return baseInstructions + "\n\n" + typeConfig.AdditionalInstructions;
        }

        return baseInstructions;
    }

    public string GetResponseFormat()
    {
        return _configuration.Value.Prompts.CompanyResearch.ResponseFormat;
    }

    public CompanyResearchPromptConfiguration GetConfiguration()
    {
        return _configuration.Value;
    }

    private CompanyResearchPromptConfiguration LoadConfiguration()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            string resourceName = "Infrastructure.AI.Prompts.company-research-prompts.yaml";
            
            using Stream? stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
            {
                _logger.LogError("Could not find embedded resource: {ResourceName}", resourceName);
                return GetDefaultConfiguration();
            }

            using var reader = new StreamReader(stream);
            string yamlContent = reader.ReadToEnd();

            IDeserializer deserializer = new DeserializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .Build();

            return deserializer.Deserialize<CompanyResearchPromptConfiguration>(yamlContent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load company research prompt configuration, using defaults");
            return GetDefaultConfiguration();
        }
    }

    private static CompanyResearchPromptConfiguration GetDefaultConfiguration()
    {
        return new CompanyResearchPromptConfiguration
        {
            Prompts = new CompanyResearchPromptsSection
            {
                CompanyResearch = new CompanyResearchPrompt
                {
                    SystemMessage = "You are an expert business researcher. Research companies and extract relevant information for job applicants.",
                    UserPromptTemplate = "Research the company at {company_url} for the position: {job_title}. {instructions} {response_format}",
                    Instructions = "1. Extract company information\n2. Focus on publicly available data\n3. Provide accurate information only",
                    ResponseFormat = "Respond with JSON containing company details and confidence score."
                }
            },
            CompanyTypes = new Dictionary<string, CompanyTypeConfig>(),
            QualityControl = new CompanyResearchQualityControlConfig
            {
                MinConfidenceThreshold = 0.4,
                MaxDescriptionLength = 500,
                MaxNewsLength = 300,
                RequiredFields = new[] { "companyName", "companyDescription", "industry" },
                ValidationRules = new[] { "Factual information only", "No assumptions" }
            }
        };
    }
}

// Configuration classes
public sealed class CompanyResearchPromptConfiguration
{
    public CompanyResearchPromptsSection Prompts { get; set; } = new();
    public Dictionary<string, CompanyTypeConfig> CompanyTypes { get; set; } = new();
    public CompanyResearchQualityControlConfig QualityControl { get; set; } = new();
    public Dictionary<string, string> ErrorMessages { get; set; } = new();
}

public sealed class CompanyResearchPromptsSection
{
    public CompanyResearchPrompt CompanyResearch { get; set; } = new();
}

public sealed class CompanyResearchPrompt
{
    public string SystemMessage { get; set; } = string.Empty;
    public string UserPromptTemplate { get; set; } = string.Empty;
    public string Instructions { get; set; } = string.Empty;
    public string ResponseFormat { get; set; } = string.Empty;
    public string FallbackInstructions { get; set; } = string.Empty;
}

public sealed class CompanyTypeConfig
{
    public string AdditionalInstructions { get; set; } = string.Empty;
}

public sealed class CompanyResearchQualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxDescriptionLength { get; set; }
    public int MaxNewsLength { get; set; }
    public string[] RequiredFields { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}
