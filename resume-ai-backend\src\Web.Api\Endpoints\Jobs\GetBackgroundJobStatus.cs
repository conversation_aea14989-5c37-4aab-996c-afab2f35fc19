using Hangfire;

namespace Web.Api.Endpoints.Jobs;

internal sealed class GetBackgroundJobStatus : IEndpoint
{
    public sealed record BackgroundJobStatusResponse(
        string JobId,
        string Status,
        string? StatusReason,
        DateTime? CreatedAt,
        DateTime? StartedAt,
        DateTime? CompletedAt,
        string? ErrorMessage);

    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("jobs/background-status/{backgroundJobId}", (string backgroundJobId) =>
        {
            try
            {
                var monitoringApi = JobStorage.Current.GetMonitoringApi();
                var jobDetails = monitoringApi.JobDetails(backgroundJobId);

                if (jobDetails == null)
                {
                    return Results.NotFound(new { Message = $"Background job with ID '{backgroundJobId}' not found." });
                }

                // Get the latest job state
                var latestState = jobDetails.History
                    .OrderByDescending(h => h.CreatedAt)
                    .FirstOrDefault();

                var status = latestState?.StateName ?? "Unknown";
                var statusReason = latestState?.Reason;
                var createdAt = jobDetails.CreatedAt;
                var startedAt = jobDetails.History
                    .Where(h => h.StateName == "Processing")
                    .OrderBy(h => h.CreatedAt)
                    .FirstOrDefault()?.CreatedAt;
                var completedAt = jobDetails.History
                    .Where(h => h.StateName == "Succeeded" || h.StateName == "Failed")
                    .OrderByDescending(h => h.CreatedAt)
                    .FirstOrDefault()?.CreatedAt;

                // Extract error message if the job failed
                string? errorMessage = null;
                if (status == "Failed")
                {
                    var failedState = jobDetails.History
                        .Where(h => h.StateName == "Failed")
                        .OrderByDescending(h => h.CreatedAt)
                        .FirstOrDefault();
                    
                    if (failedState?.Data?.ContainsKey("ExceptionMessage") == true)
                    {
                        errorMessage = failedState.Data["ExceptionMessage"];
                    }
                }

                var response = new BackgroundJobStatusResponse(
                    backgroundJobId,
                    MapHangfireStatusToFriendlyStatus(status),
                    statusReason,
                    createdAt,
                    startedAt,
                    completedAt,
                    errorMessage);

                return Results.Ok(response);
            }
            catch (Exception ex)
            {
                return Results.Problem(
                    title: "Error retrieving background job status",
                    detail: ex.Message,
                    statusCode: StatusCodes.Status500InternalServerError);
            }
        })
        .WithTags(Tags.Jobs)
        .WithName("GetBackgroundJobStatus")
        .Produces<BackgroundJobStatusResponse>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError)
        .RequireAuthorization();
    }

    private static string MapHangfireStatusToFriendlyStatus(string hangfireStatus)
    {
        return hangfireStatus switch
        {
            "Enqueued" => "Queued",
            "Processing" => "Processing",
            "Succeeded" => "Completed",
            "Failed" => "Failed",
            "Deleted" => "Cancelled",
            "Scheduled" => "Scheduled",
            _ => hangfireStatus
        };
    }
}
