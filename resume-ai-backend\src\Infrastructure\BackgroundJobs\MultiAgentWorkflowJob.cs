using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.AI.ExecuteAgentWorkflow;
using Application.JobApplications.CreateWithAICustomization;
using Application.Resumes.CreateFromAICustomization;
using Domain.AI;
using Domain.Jobs;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.BackgroundJobs;

/// <summary>
/// Background job for executing the complete multi-agent AI workflow
/// </summary>
public class MultiAgentWorkflowJob
{
    private readonly ICommandHandler<ExecuteAgentWorkflowCommand, ExecuteAgentWorkflowResponse> _workflowHandler;
    private readonly ICommandHandler<CreateResumeFromAICustomizationCommand, Guid> _createResumeHandler;
    private readonly ICommandHandler<CreateJobApplicationWithAICustomizationCommand, Guid> _createJobApplicationHandler;
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<MultiAgentWorkflowJob> _logger;

    public MultiAgentWorkflowJob(
        ICommandHandler<ExecuteAgentWorkflowCommand, ExecuteAgentWorkflowResponse> workflowHandler,
        ICommandHandler<CreateResumeFromAICustomizationCommand, Guid> createResumeHandler,
        ICommandHandler<CreateJobApplicationWithAICustomizationCommand, Guid> createJobApplicationHandler,
        IApplicationDbContext dbContext,
        ILogger<MultiAgentWorkflowJob> logger)
    {
        _workflowHandler = workflowHandler;
        _createResumeHandler = createResumeHandler;
        _createJobApplicationHandler = createJobApplicationHandler;
        _dbContext = dbContext;
        _logger = logger;
    }

    [AutomaticRetry(Attempts = 2, DelaysInSeconds = [60, 300])]
    public async Task ProcessMultiAgentWorkflowAsync(Guid jobId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting multi-agent AI workflow background job for Job ID: {JobId}", jobId);

        try
        {
            // Get job with related data
            var job = await _dbContext.Jobs
                .FirstOrDefaultAsync(j => j.Id == jobId && !j.IsDeleted, cancellationToken);

            if (job is null)
            {
                _logger.LogError("Job {JobId} not found, aborting multi-agent workflow", jobId);
                return;
            }

            // Get the user's parent resume
            var parentResume = await _dbContext.Resumes
                .AsNoTracking()
                .FirstOrDefaultAsync(r => r.UserId == job.UserId &&
                                         (r.ParentId == null || r.ParentId == Guid.Empty) &&
                                         !r.IsDeleted, cancellationToken);

            if (parentResume is null)
            {
                _logger.LogError("Parent resume not found for User {UserId}, aborting multi-agent workflow", job.UserId);
                job.UpdateStatus(JobStatus.AIProcessingFailed);
                await _dbContext.SaveChangesAsync(cancellationToken);
                return;
            }

            // Update job status to processing
            job.UpdateStatus(JobStatus.AIProcessing);
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Create a customized resume first (needed for JobApplication)
            var createResumeCommand = new CreateResumeFromAICustomizationCommand(
                job.UserId,
                parentResume.Id,
                parentResume.ResumeContent, // Will be updated by the workflow
                "Multi-agent AI workflow in progress",
                job.PreferredAIModel);

            var resumeResult = await _createResumeHandler.Handle(createResumeCommand, cancellationToken);

            if (resumeResult.IsFailure)
            {
                _logger.LogError("Failed to create resume for Job ID: {JobId}. Error: {Error}",
                    jobId, resumeResult.Error.Description);
                job.UpdateStatus(JobStatus.AIProcessingFailed);
                await _dbContext.SaveChangesAsync(cancellationToken);
                return;
            }

            // Create job application (needed for multi-agent workflow)
            var createJobApplicationCommand = new CreateJobApplicationWithAICustomizationCommand(
                jobId,
                job.UserId,
                resumeResult.Value,
                parentResume.ResumeContent, // Will be updated by the workflow
                "Multi-agent AI workflow in progress",
                Array.Empty<string>(),
                0.0,
                job.PreferredAIModel);

            var jobApplicationResult = await _createJobApplicationHandler.Handle(createJobApplicationCommand, cancellationToken);

            if (jobApplicationResult.IsFailure)
            {
                _logger.LogError("Failed to create job application for Job ID: {JobId}. Error: {Error}",
                    jobId, jobApplicationResult.Error.Description);
                job.UpdateStatus(JobStatus.AIProcessingFailed);
                await _dbContext.SaveChangesAsync(cancellationToken);
                return;
            }

            // Execute multi-agent workflow
            var workflowCommand = new ExecuteAgentWorkflowCommand(
                jobApplicationResult.Value,
                new[] {
                    AgentType.CompanyResearch,
                    AgentType.ResumeCustomization,
                    AgentType.CoverLetter,
                    AgentType.FollowUpEmail
                },
                false, // Sequential execution for context sharing
                job.PreferredAIModel);

            _logger.LogInformation("Executing multi-agent workflow for JobApplication {JobApplicationId} with sequential processing",
                jobApplicationResult.Value);

            var workflowResult = await _workflowHandler.Handle(workflowCommand, cancellationToken);

            if (workflowResult.IsSuccess)
            {
                var response = workflowResult.Value;
                
                // Update the resume with the customized content if available
                if (response.Results.TryGetValue(AgentType.ResumeCustomization, out var resumeAgentResult) &&
                    resumeAgentResult.IsSuccess && resumeAgentResult.Data is Application.Abstractions.AI.ResumeCustomizationResponse resumeResponse)
                {
                    var customizedResume = await _dbContext.Resumes
                        .FirstOrDefaultAsync(r => r.Id == resumeResult.Value, cancellationToken);
                    
                    if (customizedResume is not null)
                    {
                        customizedResume.ResumeContent = resumeResponse.CustomizedResumeContent;
                        await _dbContext.SaveChangesAsync(cancellationToken);
                    }
                }

                job.UpdateStatus(JobStatus.AIProcessingCompleted);

                _logger.LogInformation(
                    "Multi-agent AI workflow completed successfully for Job ID: {JobId}. " +
                    "Execution time: {ExecutionTime}ms. Success: {Success}. " +
                    "Created Resume: {ResumeId}, JobApplication: {JobApplicationId}",
                    jobId, response.TotalExecutionTime.TotalMilliseconds, response.IsSuccess,
                    resumeResult.Value, jobApplicationResult.Value);

                // Log individual agent results
                foreach (var (agentType, result) in response.Results)
                {
                    _logger.LogInformation(
                        "Agent {AgentType} completed for Job {JobId}. Success: {Success}, Confidence: {Confidence}",
                        agentType, jobId, result.IsSuccess, result.ConfidenceScore);
                }
            }
            else
            {
                job.UpdateStatus(JobStatus.AIProcessingFailed);
                _logger.LogError(
                    "Multi-agent workflow failed for Job ID: {JobId}. Error: {Error}",
                    jobId, workflowResult.Error.Description);
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in multi-agent workflow for Job ID: {JobId}", jobId);

            try
            {
                var job = await _dbContext.Jobs
                    .FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
                
                if (job is not null)
                {
                    job.UpdateStatus(JobStatus.AIProcessingFailed);
                    await _dbContext.SaveChangesAsync(cancellationToken);
                }
            }
            catch (Exception saveEx)
            {
                _logger.LogError(saveEx, "Failed to update job status after error for Job ID: {JobId}", jobId);
            }

            throw; // Re-throw to trigger Hangfire retry mechanism
        }
    }
}
