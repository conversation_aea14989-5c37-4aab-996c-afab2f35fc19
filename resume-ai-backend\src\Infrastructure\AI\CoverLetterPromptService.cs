using Microsoft.Extensions.Logging;
using System.Reflection;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace Infrastructure.AI;

public interface ICoverLetterPromptService
{
    string GetSystemMessage();
    string GetUserPrompt(string jobTitle, string jobDescription, string companyUrl, string resumeContent);
    string GetInstructions(string? jobType = null);
    string GetResponseFormat();
    CoverLetterPromptConfiguration GetConfiguration();
}

internal sealed class CoverLetterPromptService : ICoverLetterPromptService
{
    private readonly ILogger<CoverLetterPromptService> _logger;
    private readonly Lazy<CoverLetterPromptConfiguration> _configuration;

    public CoverLetterPromptService(ILogger<CoverLetterPromptService> logger)
    {
        _logger = logger;
        _configuration = new Lazy<CoverLetterPromptConfiguration>(LoadConfiguration);
    }

    public string GetSystemMessage()
    {
        return _configuration.Value.Prompts.CoverLetter.SystemMessage;
    }

    public string GetUserPrompt(string jobTitle, string jobDescription, string companyUrl, string resumeContent)
    {
        var template = _configuration.Value.Prompts.CoverLetter.UserPromptTemplate;
        var instructions = GetInstructions();
        var responseFormat = GetResponseFormat();

        return template
            .Replace("{job_title}", jobTitle)
            .Replace("{job_description}", jobDescription)
            .Replace("{company_url}", companyUrl)
            .Replace("{resume_content}", resumeContent)
            .Replace("{company_research}", "")
            .Replace("{instructions}", instructions)
            .Replace("{response_format}", responseFormat);
    }

    public string GetInstructions(string? jobType = null)
    {
        var baseInstructions = _configuration.Value.Prompts.CoverLetter.Instructions;

        if (!string.IsNullOrEmpty(jobType) && 
            _configuration.Value.JobTypes.TryGetValue(jobType, out var typeConfig))
        {
            return baseInstructions + "\n\n" + typeConfig.AdditionalInstructions;
        }

        return baseInstructions;
    }

    public string GetResponseFormat()
    {
        return _configuration.Value.Prompts.CoverLetter.ResponseFormat;
    }

    public CoverLetterPromptConfiguration GetConfiguration()
    {
        return _configuration.Value;
    }

    private CoverLetterPromptConfiguration LoadConfiguration()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            string resourceName = "Infrastructure.AI.Prompts.cover-letter-prompts.yaml";
            
            using Stream? stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
            {
                _logger.LogError("Could not find embedded resource: {ResourceName}", resourceName);
                return GetDefaultConfiguration();
            }

            using var reader = new StreamReader(stream);
            string yamlContent = reader.ReadToEnd();

            IDeserializer deserializer = new DeserializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .Build();

            return deserializer.Deserialize<CoverLetterPromptConfiguration>(yamlContent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load cover letter prompt configuration, using defaults");
            return GetDefaultConfiguration();
        }
    }

    private static CoverLetterPromptConfiguration GetDefaultConfiguration()
    {
        return new CoverLetterPromptConfiguration
        {
            Prompts = new CoverLetterPromptsSection
            {
                CoverLetter = new CoverLetterPrompt
                {
                    SystemMessage = "You are an expert career counselor and professional writer. Generate compelling cover letters.",
                    UserPromptTemplate = "Generate a cover letter for {job_title} at {company_url}. Job: {job_description}. Resume: {resume_content}. {instructions} {response_format}",
                    Instructions = "1. Create professional cover letter\n2. Highlight relevant experience\n3. Show genuine interest",
                    ResponseFormat = "Respond with JSON containing coverLetterContent, summary, keyHighlights, and confidence."
                }
            },
            JobTypes = new Dictionary<string, JobTypeConfig>(),
            QualityControl = new CoverLetterQualityControlConfig
            {
                MinConfidenceThreshold = 0.5,
                MaxContentLength = 20000,
                MinContentLength = 500,
                RequiredElements = new[] { "professional greeting", "opening statement", "body paragraphs", "closing statement" },
                ValidationRules = new[] { "Professional tone", "Specific examples", "Tailored content" }
            }
        };
    }
}

// Configuration classes
public sealed class CoverLetterPromptConfiguration
{
    public CoverLetterPromptsSection Prompts { get; set; } = new();
    public Dictionary<string, JobTypeConfig> JobTypes { get; set; } = new();
    public CoverLetterQualityControlConfig QualityControl { get; set; } = new();
    public Dictionary<string, string> ErrorMessages { get; set; } = new();
}

public sealed class CoverLetterPromptsSection
{
    public CoverLetterPrompt CoverLetter { get; set; } = new();
}

public sealed class CoverLetterPrompt
{
    public string SystemMessage { get; set; } = string.Empty;
    public string UserPromptTemplate { get; set; } = string.Empty;
    public string Instructions { get; set; } = string.Empty;
    public string ResponseFormat { get; set; } = string.Empty;
    public string FallbackInstructions { get; set; } = string.Empty;
}

public sealed class JobTypeConfig
{
    public string AdditionalInstructions { get; set; } = string.Empty;
}

public sealed class CoverLetterQualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxContentLength { get; set; }
    public int MinContentLength { get; set; }
    public string[] RequiredElements { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}
