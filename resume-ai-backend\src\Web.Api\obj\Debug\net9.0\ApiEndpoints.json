[{"ContainingType": "Web.Api.Endpoints.JobApplications.Create+<>c", "Method": "<MapEndpoint>b__1_0", "RelativePath": "job-applications", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Api.Endpoints.JobApplications.Create+CreateJobApplicationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Guid", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["job-applications"], "EndpointName": "CreateJobApplication"}, {"ContainingType": "Web.Api.Endpoints.JobApplications.GetById+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "job-applications/{id:guid}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Application.JobApplications.GetById.JobApplicationResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["job-applications"], "EndpointName": "GetJobApplicationById"}, {"ContainingType": "Web.Api.Endpoints.JobApplications.UpdateStatus+<>c", "Method": "<MapEndpoint>b__1_0", "RelativePath": "job-applications/{jobApplicationId:guid}/status", "HttpMethod": "PATCH", "IsController": false, "Order": 0, "Parameters": [{"Name": "jobApplicationId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "Web.Api.Endpoints.JobApplications.UpdateStatus+UpdateJobApplicationStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["job-applications"], "EndpointName": "UpdateJobApplicationStatus"}, {"ContainingType": "Web.Api.Endpoints.Jobs.Create+<>c", "Method": "<MapEndpoint>b__1_0", "RelativePath": "jobs", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Api.Endpoints.Jobs.Create+CreateJobRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Guid", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["jobs"], "EndpointName": "<PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "Web.Api.Endpoints.Jobs.GetById+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "jobs/{id:guid}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Application.Jobs.GetById.JobResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["jobs"], "EndpointName": "GetJobById"}, {"ContainingType": "Web.Api.Endpoints.Jobs.Update+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "jobs/{id:guid}", "HttpMethod": "PUT", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "Web.Api.Endpoints.Jobs.UpdateJobRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["jobs"], "EndpointName": "Update<PERSON><PERSON>"}, {"ContainingType": "Web.Api.Endpoints.Resumes.Create", "Method": "CreateResumeAsync", "RelativePath": "resumes", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Api.Endpoints.Resumes.Create+Request", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["resumes"], "EndpointName": "CreateResume"}, {"ContainingType": "Web.Api.Endpoints.Resumes.GetById", "Method": "GetResumeAsync", "RelativePath": "resumes/{id:guid}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Application.Resumes.GetById.ResumeResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["resumes"], "EndpointName": "GetResumeById"}, {"ContainingType": "Web.Api.Endpoints.Resumes.Download+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "resumes/{id:guid}/download", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["resumes"], "EndpointName": "DownloadResume"}, {"ContainingType": "Web.Api.Endpoints.Resumes.Upload", "Method": "UploadResumeAsync", "RelativePath": "resumes/upload", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "parentId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Guid", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Http.HttpValidationProblemDetails", "MediaTypes": ["application/problem+json"], "StatusCode": 400}], "Tags": ["resumes"], "EndpointName": "UploadResume"}, {"ContainingType": "Web.Api.Endpoints.Todos.Create+<>c", "Method": "<MapEndpoint>b__1_0", "RelativePath": "todos", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Api.Endpoints.Todos.Create+Request", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["todos"]}, {"ContainingType": "Web.Api.Endpoints.Todos.Get+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "todos", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["todos"]}, {"ContainingType": "Web.Api.Endpoints.Todos.Delete+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "todos/{id:guid}", "HttpMethod": "DELETE", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["todos"]}, {"ContainingType": "Web.Api.Endpoints.Todos.GetById+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "todos/{id:guid}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["todos"]}, {"ContainingType": "Web.Api.Endpoints.Todos.Complete+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "todos/{id:guid}/complete", "HttpMethod": "PUT", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["todos"]}, {"ContainingType": "Web.Api.Endpoints.Users.GetById+<>c", "Method": "<MapEndpoint>b__0_0", "RelativePath": "users/{userId}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["Users"]}, {"ContainingType": "Web.Api.Endpoints.Users.Login+<>c", "Method": "<MapEndpoint>b__1_0", "RelativePath": "users/login", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Api.Endpoints.Users.Login+Request", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["Users"]}, {"ContainingType": "Web.Api.Endpoints.Users.Register+<>c", "Method": "<MapEndpoint>b__1_0", "RelativePath": "users/register", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Api.Endpoints.Users.Register+Request", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["Users"]}]