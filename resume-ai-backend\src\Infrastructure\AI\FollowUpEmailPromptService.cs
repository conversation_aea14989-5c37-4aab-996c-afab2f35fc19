using Application.Abstractions.AI;
using Microsoft.Extensions.Logging;
using System.Reflection;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace Infrastructure.AI;

public interface IFollowUpEmailPromptService
{
    string GetSystemMessage();
    string GetUserPrompt(string jobTitle, string companyUrl, DateTime applicationDate, FollowUpEmailType emailType);
    string GetInstructions(FollowUpEmailType emailType);
    string GetResponseFormat();
    FollowUpEmailPromptConfiguration GetConfiguration();
}

internal sealed class FollowUpEmailPromptService : IFollowUpEmailPromptService
{
    private readonly ILogger<FollowUpEmailPromptService> _logger;
    private readonly Lazy<FollowUpEmailPromptConfiguration> _configuration;

    public FollowUpEmailPromptService(ILogger<FollowUpEmailPromptService> logger)
    {
        _logger = logger;
        _configuration = new Lazy<FollowUpEmailPromptConfiguration>(LoadConfiguration);
    }

    public string GetSystemMessage()
    {
        return _configuration.Value.Prompts.FollowUpEmail.SystemMessage;
    }

    public string GetUserPrompt(string jobTitle, string companyUrl, DateTime applicationDate, FollowUpEmailType emailType)
    {
        var template = _configuration.Value.Prompts.FollowUpEmail.UserPromptTemplate;
        var instructions = GetInstructions(emailType);
        var responseFormat = GetResponseFormat();

        return template
            .Replace("{job_title}", jobTitle)
            .Replace("{company_url}", companyUrl)
            .Replace("{application_date}", applicationDate.ToString("yyyy-MM-dd"))
            .Replace("{email_type}", emailType.ToString())
            .Replace("{company_research}", "")
            .Replace("{instructions}", instructions)
            .Replace("{response_format}", responseFormat);
    }

    public string GetInstructions(FollowUpEmailType emailType)
    {
        var baseInstructions = _configuration.Value.Prompts.FollowUpEmail.Instructions;
        var emailTypeKey = emailType.ToString().ToLowerInvariant();

        if (_configuration.Value.EmailTypes.TryGetValue(emailTypeKey, out var typeConfig))
        {
            return baseInstructions + "\n\n**Email Type Specific Instructions:**\n" + typeConfig.AdditionalInstructions;
        }

        return baseInstructions;
    }

    public string GetResponseFormat()
    {
        return _configuration.Value.Prompts.FollowUpEmail.ResponseFormat;
    }

    public FollowUpEmailPromptConfiguration GetConfiguration()
    {
        return _configuration.Value;
    }

    private FollowUpEmailPromptConfiguration LoadConfiguration()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            string resourceName = "Infrastructure.AI.Prompts.follow-up-email-prompts.yaml";
            
            using Stream? stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
            {
                _logger.LogError("Could not find embedded resource: {ResourceName}", resourceName);
                return GetDefaultConfiguration();
            }

            using var reader = new StreamReader(stream);
            string yamlContent = reader.ReadToEnd();

            IDeserializer deserializer = new DeserializerBuilder()
                .WithNamingConvention(UnderscoredNamingConvention.Instance)
                .Build();

            return deserializer.Deserialize<FollowUpEmailPromptConfiguration>(yamlContent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load follow-up email prompt configuration, using defaults");
            return GetDefaultConfiguration();
        }
    }

    private static FollowUpEmailPromptConfiguration GetDefaultConfiguration()
    {
        return new FollowUpEmailPromptConfiguration
        {
            Prompts = new FollowUpEmailPromptsSection
            {
                FollowUpEmail = new FollowUpEmailPrompt
                {
                    SystemMessage = "You are an expert career coach and professional communication specialist. Generate effective follow-up emails.",
                    UserPromptTemplate = "Generate a follow-up email for {job_title} at {company_url}. Applied on: {application_date}. Type: {email_type}. {instructions} {response_format}",
                    Instructions = "1. Create professional follow-up email\n2. Show continued interest\n3. Respect professional boundaries",
                    ResponseFormat = "Respond with JSON containing emailSubject, emailContent, summary, emailType, and confidence."
                }
            },
            EmailTypes = new Dictionary<string, EmailTypeConfig>(),
            QualityControl = new FollowUpEmailQualityControlConfig
            {
                MinConfidenceThreshold = 0.5,
                MaxContentLength = 2000,
                MinContentLength = 200,
                MaxSubjectLength = 60,
                RequiredElements = new[] { "professional greeting", "purpose statement", "main content", "professional closing" },
                ValidationRules = new[] { "Professional tone", "Concise content", "Clear call-to-action" }
            }
        };
    }
}

// Configuration classes
public sealed class FollowUpEmailPromptConfiguration
{
    public FollowUpEmailPromptsSection Prompts { get; set; } = new();
    public Dictionary<string, EmailTypeConfig> EmailTypes { get; set; } = new();
    public Dictionary<string, IndustryConfig> Industries { get; set; } = new();
    public FollowUpEmailQualityControlConfig QualityControl { get; set; } = new();
    public Dictionary<string, string> ErrorMessages { get; set; } = new();
}

public sealed class FollowUpEmailPromptsSection
{
    public FollowUpEmailPrompt FollowUpEmail { get; set; } = new();
}

public sealed class FollowUpEmailPrompt
{
    public string SystemMessage { get; set; } = string.Empty;
    public string UserPromptTemplate { get; set; } = string.Empty;
    public string Instructions { get; set; } = string.Empty;
    public string ResponseFormat { get; set; } = string.Empty;
    public string FallbackInstructions { get; set; } = string.Empty;
}

public sealed class EmailTypeConfig
{
    public string Timing { get; set; } = string.Empty;
    public string AdditionalInstructions { get; set; } = string.Empty;
}

public sealed class IndustryConfig
{
    public string Tone { get; set; } = string.Empty;
    public string Focus { get; set; } = string.Empty;
}

public sealed class FollowUpEmailQualityControlConfig
{
    public double MinConfidenceThreshold { get; set; }
    public int MaxContentLength { get; set; }
    public int MinContentLength { get; set; }
    public int MaxSubjectLength { get; set; }
    public string[] RequiredElements { get; set; } = Array.Empty<string>();
    public string[] ValidationRules { get; set; } = Array.Empty<string>();
}
